<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_activity_graph" modulePackage="com.tech.ekvayu" filePath="app\src\main\res\layout\fragment_activity_graph.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView" rootNodeViewId="@+id/main"><Targets><Target id="@+id/main" tag="layout/fragment_activity_graph_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="225" endOffset="12"/></Target><Target id="@+id/tvHeader" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="33" endOffset="55"/></Target><Target id="@+id/tvSubTitle" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="47" endOffset="44"/></Target><Target id="@+id/pieChart" view="com.github.mikephil.charting.charts.PieChart"><Expressions/><location startLine="50" startOffset="8" endLine="56" endOffset="36"/></Target><Target id="@+id/summaryCards" view="LinearLayout"><Expressions/><location startLine="59" startOffset="8" endLine="170" endOffset="22"/></Target><Target id="@+id/tvDispute" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="91" endOffset="56"/></Target><Target id="@+id/tvSpam" view="TextView"><Expressions/><location startLine="116" startOffset="16" endLine="125" endOffset="21"/></Target><Target id="@+id/tvProcessed" view="TextView"><Expressions/><location startLine="150" startOffset="16" endLine="158" endOffset="52"/></Target></Targets></Layout>