package com.tech.ekvayu.Fragments

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import com.tech.ekvayu.Adapter.SpamMailAdapter
import com.tech.ekvayu.ApiConfig.ApiClient
import com.tech.ekvayu.ApiConfig.ApiService
import com.tech.ekvayu.BaseClass.AppConstant
import com.tech.ekvayu.BaseClass.CommonUtil
import com.tech.ekvayu.BaseClass.SharedPrefManager
import com.tech.ekvayu.Request.CommonRequest
import com.tech.ekvayu.Response.Results
import com.tech.ekvayu.Response.SpamMailResponse
import com.tech.ekvayu.databinding.FragmentSpamMailBinding
import com.tech.ekvayu.databinding.LayoutSpamDetailBinding
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response


class SpamMailFragment : Fragment() , SpamMailAdapter.OnSpamMailClickListener{
    private val sharedPrefManager by lazy { SharedPrefManager.getInstance() }


    private lateinit var binding: FragmentSpamMailBinding
    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {

        binding= FragmentSpamMailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loadSpamMails()
    }

    private fun loadSpamMails() {
        val receiverEmail = sharedPrefManager.getString(AppConstant.receiverMail,"")
        val isMailConfigured = sharedPrefManager.getBoolean(AppConstant.isMailConfigured, false)
        val learnedEmail = sharedPrefManager.getString(AppConstant.userPrimaryEmail, "")

        Log.d("SpamMailFragment", "=== EMAIL CONFIGURATION DEBUG ===")
        Log.d("SpamMailFragment", "Receiver email: '$receiverEmail'")
        Log.d("SpamMailFragment", "Is mail configured: $isMailConfigured")
        Log.d("SpamMailFragment", "Learned email: '$learnedEmail'")
        Log.d("SpamMailFragment", "Default email: '${AppConstant.defaultReceiverEmail}'")

        if (receiverEmail.isNotEmpty()) {
            getSpamMail(receiverEmail)
        } else {
            showEmptyEmailError()
        }
    }


    private fun getSpamMail(email: String) {
        Log.d("SpamMailFragment", "Making API call with email: '$email'")

        val retrofit = ApiClient.getRetrofitInstance(requireContext())
        val apiService = retrofit!!.create(ApiService::class.java)

        val request = CommonRequest(emailId = email)

        CommonUtil.showProgressDialog(requireContext(), "Loading spam mails...")
        apiService.getSpamMail(request)
            .enqueue(object : Callback<SpamMailResponse> {
                override fun onResponse(
                    call: Call<SpamMailResponse>,
                    response: Response<SpamMailResponse>
                ) {
                    CommonUtil.hideProgressDialog()
                    Log.d("SpamMailFragment", "API Response: ${response.code()}, Success: ${response.isSuccessful}")

                    if (response.isSuccessful && response.body() != null) {
                        val spamMails = response.body()!!.results
                        Log.d("SpamMailFragment", "Received ${spamMails.size} spam mails")

                        if (spamMails.isNotEmpty()) {
                            binding.rvSpamMail.visibility = View.VISIBLE
                            binding.tvEmptyState.visibility = View.GONE
                            val adapter = SpamMailAdapter(spamMails, this@SpamMailFragment)
                            binding.rvSpamMail.adapter = adapter
                            binding.rvSpamMail.layoutManager = GridLayoutManager(requireActivity(), 1)
                        } else {
                            binding.rvSpamMail.visibility = View.GONE
                            binding.tvEmptyState.visibility = View.VISIBLE
                            showEmptySpamList()
                        }
                    } else {
                        val errorMsg = "Failed to load spam mails: ${response.message()}"
                        Log.e("SpamMailFragment", errorMsg)
                        Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_LONG).show()
                    }
                }

                override fun onFailure(call: Call<SpamMailResponse?>, t: Throwable) {
                    CommonUtil.hideProgressDialog()
                    val errorMsg = "Network error: ${t.message}"
                    Log.e("SpamMailFragment", errorMsg, t)
                    Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_LONG).show()
                }
            })
    }

    override fun onSpamClicked(item: Results) {
        showCustomEmailDialog(requireContext(),item)
    }

    private fun showCustomEmailDialog(context: Context, item: Results) {
        val binding = LayoutSpamDetailBinding.inflate(LayoutInflater.from(context))
        binding.tvSubject.text = item.subject
        binding.tvSender.text = "From: ${item.sendersEmail}"
        binding.tvReceiver.text = "To: ${item.recieversEmail}"
        binding.tvStatus.text = "Status: ${item.status}"
        binding.tvBody.text = item.emailBody.toString()

        AlertDialog.Builder(context)
            .setView(binding.root)
            .setCancelable(true)
            .setPositiveButton("Close", null)
            .show()
    }

    private fun showEmptyEmailError() {
        binding.rvSpamMail.visibility = View.GONE
        binding.tvEmptyState.visibility = View.VISIBLE
        binding.tvEmptyState.text = "No email configured.\nPlease process some emails first to configure your account. 📧"

        // Try to use default email as fallback
        val defaultEmail = AppConstant.defaultReceiverEmail
        if (defaultEmail.isNotEmpty()) {
            Log.d("SpamMailFragment", "Trying with default email: $defaultEmail")
            sharedPrefManager.putString(AppConstant.receiverMail, defaultEmail)
            getSpamMail(defaultEmail)
            return
        }

        Toast.makeText(
            requireContext(),
            "No email configured. Please process some emails first to configure your account.",
            Toast.LENGTH_LONG
        ).show()
        Log.w("SpamMailFragment", "Cannot load spam mails: No receiver email configured")
    }

    private fun showEmptySpamList() {
        Toast.makeText(
            requireContext(),
            "No spam mails found. This is good news - your emails are safe!",
            Toast.LENGTH_LONG
        ).show()
        Log.i("SpamMailFragment", "No spam mails found for user")
    }





}