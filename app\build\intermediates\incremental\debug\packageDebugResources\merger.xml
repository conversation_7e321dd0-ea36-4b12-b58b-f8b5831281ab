<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res"><file name="activity" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\activity.xml" qualifiers="" type="drawable"/><file name="argument" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\argument.xml" qualifiers="" type="drawable"/><file name="back" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\back.xml" qualifiers="" type="drawable"/><file name="bg_alert_dialog" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_alert_dialog.xml" qualifiers="" type="drawable"/><file name="bg_anim_button" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_anim_button.xml" qualifiers="" type="drawable"/><file name="bg_button_app_color" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_button_app_color.xml" qualifiers="" type="drawable"/><file name="bg_button_grey_color" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_button_grey_color.xml" qualifiers="" type="drawable"/><file name="bg_button_white" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_button_white.xml" qualifiers="" type="drawable"/><file name="bg_dialog" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_dialog.xml" qualifiers="" type="drawable"/><file name="bg_search_bar" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_search_bar.xml" qualifiers="" type="drawable"/><file name="bg_status" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\bg_status.xml" qualifiers="" type="drawable"/><file name="details" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\details.xml" qualifiers="" type="drawable"/><file name="device_d" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\device_d.png" qualifiers="" type="drawable"/><file name="device_details" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\device_details.xml" qualifiers="" type="drawable"/><file name="dispute" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute.xml" qualifiers="" type="drawable"/><file name="dispute_" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_.xml" qualifiers="" type="drawable"/><file name="dispute_mails" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\dispute_mails.xml" qualifiers="" type="drawable"/><file name="edit_text_bg" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\edit_text_bg.xml" qualifiers="" type="drawable"/><file name="ic_attachment" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_attachment.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="mail_raised" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\mail_raised.xml" qualifiers="" type="drawable"/><file name="process" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\process.xml" qualifiers="" type="drawable"/><file name="right_arrow" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\right_arrow.xml" qualifiers="" type="drawable"/><file name="spam_mail" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\spam_mail.xml" qualifiers="" type="drawable"/><file name="table_background" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\table_background.xml" qualifiers="" type="drawable"/><file name="icon_secure_phishing" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-hdpi\icon_secure_phishing.png" qualifiers="hdpi-v4" type="drawable"/><file name="theme" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-hdpi\theme.png" qualifiers="hdpi-v4" type="drawable"/><file name="icon_secure_phishing" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-mdpi\icon_secure_phishing.png" qualifiers="mdpi-v4" type="drawable"/><file name="theme" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-mdpi\theme.png" qualifiers="mdpi-v4" type="drawable"/><file name="icon_secure_phishing" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xhdpi\icon_secure_phishing.png" qualifiers="xhdpi-v4" type="drawable"/><file name="theme" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xhdpi\theme.png" qualifiers="xhdpi-v4" type="drawable"/><file name="icon_secure_phishing" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xxhdpi\icon_secure_phishing.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="theme" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xxhdpi\theme.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="icon_secure_phishing" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xxxhdpi\icon_secure_phishing.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="theme" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable-xxxhdpi\theme.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="roboto_semi_bold" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\font\roboto_semi_bold.ttf" qualifiers="" type="font"/><file name="roboto_semi_medium" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\font\roboto_semi_medium.ttf" qualifiers="" type="font"/><file name="roboto_semi_regular" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\font\roboto_semi_regular.ttf" qualifiers="" type="font"/><file name="activity_dashboard" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_dashboard.xml" qualifiers="" type="layout"/><file name="activity_email_auth" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_auth.xml" qualifiers="" type="layout"/><file name="activity_email_demo" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_demo.xml" qualifiers="" type="layout"/><file name="activity_email_detail" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_email_detail.xml" qualifiers="" type="layout"/><file name="activity_gmail_auth" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_gmail_auth.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_yahoo_auth" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\activity_yahoo_auth.xml" qualifiers="" type="layout"/><file name="fragment_activity_graph" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_activity_graph.xml" qualifiers="" type="layout"/><file name="fragment_device_details" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_device_details.xml" qualifiers="" type="layout"/><file name="fragment_dispute_maillist" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_dispute_maillist.xml" qualifiers="" type="layout"/><file name="fragment_home" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_raise_bottom_sheet" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_raise_bottom_sheet.xml" qualifiers="" type="layout"/><file name="fragment_spam_mail" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_spam_mail.xml" qualifiers="" type="layout"/><file name="fragment_suggetion_bottom" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_suggetion_bottom.xml" qualifiers="" type="layout"/><file name="fragment_warning_bottom_sheet" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_warning_bottom_sheet.xml" qualifiers="" type="layout"/><file name="header_layout" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\header_layout.xml" qualifiers="" type="layout"/><file name="item_dashboard_menu" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_dashboard_menu.xml" qualifiers="" type="layout"/><file name="item_dispute_list" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_dispute_list.xml" qualifiers="" type="layout"/><file name="item_email" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_email.xml" qualifiers="" type="layout"/><file name="item_spam_mail" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\item_spam_mail.xml" qualifiers="" type="layout"/><file name="layout_dispute_raise" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_dispute_raise.xml" qualifiers="" type="layout"/><file name="layout_progress" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_progress.xml" qualifiers="" type="layout"/><file name="layout_progress_dialog" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_progress_dialog.xml" qualifiers="" type="layout"/><file name="layout_spam_detail" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_spam_detail.xml" qualifiers="" type="layout"/><file name="layout_validation" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\layout_validation.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ai_loading" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\raw\ai_loading.json" qualifiers="" type="raw"/><file name="safe" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\raw\safe.json" qualifiers="" type="raw"/><file name="unsafe" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\raw\unsafe.json" qualifiers="" type="raw"/><file name="wait" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\raw\wait.json" qualifiers="" type="raw"/><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="app_color">#FF000080</color><color name="transparent_black">#CC000000</color><color name="transparent_black_60">#99000000</color><color name="red">#FF1100</color><color name="green">#046A08</color><color name="grey">#DCDCDC</color><color name="light_grey">#E9E9E9</color><color name="dark_grey">#6B6A6A</color><color name="transparent">#00000000</color><color name="border_dark">#505050</color><color name="text_primary_light">#212529</color><color name="border">@color/border_light</color><color name="background">@color/background_light</color><color name="background_light">#FFFFFF</color><color name="surface_dark">#1E1E1E</color><color name="text_secondary">@color/text_secondary_light</color><color name="divider">@color/divider_light</color><color name="text_primary">@color/text_primary_light</color><color name="surface_light">#F8F9FA</color><color name="surface">@color/surface_light</color><color name="divider_dark">#404040</color><color name="background_dark">#121212</color><color name="border_light">#DEE2E6</color><color name="card_background">@color/card_background_light</color><color name="text_primary_dark">#FFFFFF</color><color name="app_color_light">#FF4040B0</color><color name="app_color_dark">#FF000060</color><color name="divider_light">#E9ECEF</color><color name="card_background_light">#FFFFFF</color><color name="text_secondary_dark">#B3B3B3</color><color name="blue">#0066CC</color><color name="text_secondary_light">#6C757D</color><color name="orange">#FF8C00</color><color name="card_background_dark">#2D2D2D</color></file><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\integers.xml" qualifiers=""><integer name="google_play_services_version">12451000</integer></file><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Ekvayu</string><string name="permission">Permission</string><string name="accessibility_service_gmail">Monitors for mail in Gmail.</string><string name="accessibility_service_yahoo">Monitors for mail in yahoo.</string><string name="accessibility_service_outlook">Monitors for mail in outlook.</string><string name="warning_this_email_is_unsafe_proceed_with_caution">Warning: This email is unsafe! Proceed with caution.</string><string name="close">Close</string><string name="continuee">Continue</string><string name="google_client_id">408481275944-9clep0h2rardm0f07ugg6f765ik6bpnb.apps.googleusercontent.com</string><string name="appAuthRedirectScheme">https://ekvayuy.firebaseapp.com/__/auth/handler</string><string name="gmail_auth">Gmail Auth</string><string name="yahoo_auth">Yahoo Auth</string><string name="secure_my_app_from_phishing">Shield your inbox from phishing, malicious emails, and unauthorized access with robust security 🔒 🛡️ 🔐 🔑 measures to keep your personal information safe and secure.</string><string name="default_web_client_id" translatable="false">206367517092-15gou6mdicehhh21r1f6ru1suolkglab.apps.googleusercontent.com</string><string name="device_details">Device Details</string><string name="hello_blank_fragment">Hello blank fragment</string><string name="safe">safe</string><string name="unsafe">unsafe</string><string name="submit">Submit</string><string name="refresh">Refresh</string><string name="raise_dispute">Raise Dispute</string><string name="basic_information">Basic Information</string><string name="manufacturer_name">Manufacturer Name</string><string name="android_version">Android Version</string><string name="device_name">Device Name</string><string name="manufacturer">Manufacturer</string><string name="model_name">Model Name</string><string name="brand_name">Brand Name</string><string name="product">Product</string><string name="api_level">Api Level</string><string name="build_number">Build Number</string><string name="serial_number">Serial Number</string><string name="android_id">Android Id</string><string name="screen_info">Screen Info</string><string name="android_info">Android Info</string><string name="resolution">Resolution</string><string name="density_factor">Density Factor</string><string name="scaled_density">Scaled Density</string><string name="app_info">App Info</string><string name="version">Version</string><string name="min_sdk">Min SDK</string><string name="get_stared_raised_disputes_against">Get Started! Raised disputes against</string><string name="message_id">Message Id</string><string name="sender_email">Sender Email</string><string name="status">Status</string><string name="counter">Counter</string><string name="reason_of_raising_dispute">Reason Of Raising Dispute</string><string name="to_extract_email_details_securely">To extract email details securely, we need Accessibility Service permission. This helps us identify and analyze email content directly from your mail app to keep you safe. No personal data is stored or shared.</string><string name="i_am_facing_difficulties_retrieving_the_recipient_s_email_id_could_you_kindly_enable_the_service_and_assist_in_verifying_the_mail_for_a_timely_resolution">I am facing difficulties retrieving the recipient\'s email ID. Could you kindly enable the service and assist in verifying the mail for a timely resolution?</string></file><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/app_color</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">true</item>

        
        <item name="android:windowBackground">@color/background</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style><style name="Theme.Ekvayu" parent="Base.Theme.Ekvayu"/><style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style><style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="HeaderTitleStyle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style><style name="HeaderStyle">
        <item name="android:background">@color/surface</item>
        <item name="android:elevation">4dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style><style name="HeaderButtonStyle">
        <item name="cardBackgroundColor">@color/app_color</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style><style name="HeaderThemeButtonStyle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">2dp</item>
    </style></file><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowBackground">@color/background</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style><style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style><style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style><style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style><style name="HeaderButtonStyle">
        <item name="cardBackgroundColor">@color/app_color</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">4dp</item>
    </style><style name="HeaderTitleStyle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style><style name="HeaderThemeButtonStyle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">4dp</item>
    </style><style name="HeaderStyle">
        <item name="android:background">@color/surface</item>
        <item name="android:elevation">8dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style></file><file name="backup_rules" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="gmail_accessibility_service_config" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\xml\gmail_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="outlook_accessibility_service_config" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\xml\outlook_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="yahoo_accessibility_service_config" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\xml\yahoo_accessibility_service_config.xml" qualifiers="" type="xml"/><file name="theme_icon" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\drawable\theme_icon.xml" qualifiers="" type="drawable"/><file name="fragment_theme_settings_bottom_sheet" path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\layout\fragment_theme_settings_bottom_sheet.xml" qualifiers="" type="layout"/><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="background">@color/background_dark</color><color name="surface">@color/surface_dark</color><color name="card_background">@color/card_background_dark</color><color name="text_primary">@color/text_primary_dark</color><color name="text_secondary">@color/text_secondary_dark</color><color name="divider">@color/divider_dark</color><color name="border">@color/border_dark</color><color name="app_color">#FF4040B0</color><color name="app_color_light">#FF6060D0</color><color name="app_color_dark">#FF2020A0</color></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\build\generated\res\resValues\debug"/><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\build\generated\res\resValues\debug"/><source path="D:\Ankur\EkvayuProjects\Ekvayu\app\build\generated\res\processDebugGoogleServices"><file path="D:\Ankur\EkvayuProjects\Ekvayu\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">206367517092-4ujn198g73oklcf96ql11pd4kh65d3c9.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">206367517092</string><string name="google_api_key" translatable="false">AIzaSyAYJyBc1w4EMC7KoLsoKncEK4evTPzK4SU</string><string name="google_app_id" translatable="false">1:206367517092:android:2b4d293336c444f9fa7931</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAYJyBc1w4EMC7KoLsoKncEK4evTPzK4SU</string><string name="google_storage_bucket" translatable="false">ekvayuy.firebasestorage.app</string><string name="project_id" translatable="false">ekvayuy</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>