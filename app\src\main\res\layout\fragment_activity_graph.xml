<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/main"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Fragments.ActivityGraphFragment">

<!--    <com.github.mikephil.charting.charts.PieChart
        app:layout_constraintTop_toTopOf="parent"
        android:id="@+id/pieChart"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />-->


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <!-- Header Title -->
        <TextView
            android:id="@+id/tvHeader"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Email Activity Report"
            android:textSize="@dimen/_12sdp"
            android:textStyle="bold"
            android:textColor="@color/dark_grey"
            android:gravity="start"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Sub Title -->
        <TextView
            android:id="@+id/tvSubTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Latest scan summary threat insights"
            android:textSize="@dimen/_11sdp"
            android:textColor="#6D6D72"
            android:gravity="start"
            app:layout_constraintTop_toBottomOf="@id/tvHeader"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="4dp" />


        <com.github.mikephil.charting.charts.PieChart
            android:id="@+id/pieChart"
            app:layout_constraintTop_toBottomOf="@+id/tvSubTitle"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_200sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:padding="10dp" />

        <!-- Quick Stats: 3 cards -->
        <LinearLayout
            android:id="@+id/summaryCards"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@id/pieChart"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- Dispute Mail -->
            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/card_background"
                android:layout_margin="4dp"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:padding="12dp"
                android:gravity="center"
                android:backgroundTintMode="src_in"
                android:elevation="2dp">

                <TextView
                    android:id="@+id/tvDispute"
                    android:text="🟩 0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/green"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"/>

                <TextView
                    android:text="Dispute"
                    android:textSize="14sp"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:textColor="@color/green"
                    android:fontFamily="@font/roboto_semi_regular"
                    />

            </LinearLayout>

            <!-- Spam Mail -->
            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/card_background"
                android:layout_margin="4dp"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:padding="12dp"
                android:gravity="center"
                android:elevation="2dp">

                <TextView
                    android:id="@+id/tvSpam"
                    android:text="🟨 0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:textColor="@color/orange"
                    />

                <TextView
                    android:text="Spam"
                    android:textSize="14sp"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:fontFamily="@font/roboto_semi_regular"
                    android:textColor="@color/orange"
                    />

            </LinearLayout>

            <!-- Process Mail -->
            <LinearLayout
                android:layout_weight="1"
                android:orientation="vertical"
                android:background="@color/card_background"
                android:layout_margin="4dp"
                android:layout_width="0dp"
                android:layout_height="100dp"
                android:padding="12dp"
                android:gravity="center"
                android:elevation="2dp">

                <TextView
                    android:id="@+id/tvProcessed"
                    android:text="🟥 0"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:fontFamily="@font/roboto_semi_medium"
                    android:textColor="@color/red" />

                <TextView
                    android:text="Processed"
                    android:textSize="14sp"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:fontFamily="@font/roboto_semi_regular"
                    android:textColor="@color/red" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            app:layout_constraintTop_toBottomOf="@+id/summaryCards"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            android:layout_marginTop="@dimen/_16sdp"

            >

            <TextView
                android:text="📅 Last Scan: 24 June 2025"
                android:textSize="@dimen/_11sdp"
                android:textColor="#555555"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp"
                android:fontFamily="@font/roboto_semi_regular"
                />


            <TextView
                android:text="📈 Highest Risk Day: 21 June (14 Spam)"
                android:textColor="@color/red"
                android:textSize="@dimen/_11sdp"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:layout_marginBottom="6dp"
                android:fontFamily="@font/roboto_semi_regular"
                />


            <TextView
                android:text="🔄 Scans this week: 112"
                android:textColor="@color/blue"
                android:textSize="@dimen/_11sdp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="6dp" />

            <TextView
                android:text="🧠 AI Decisions: 94% Accurate"
                android:textSize="@dimen/_11sdp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:fontFamily="@font/roboto_semi_regular"
                />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</ScrollView>