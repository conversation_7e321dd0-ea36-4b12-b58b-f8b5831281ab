{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10.2\\com.tech.ekvayu.app-mergeDebugResources-37:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a789a3e91da4aac57216b80449d8079\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "82,83,84,85,86,87,88,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3571,3641,3725,3809,3905,4007,4109,6964", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3636,3720,3804,3900,4002,4104,4198,7048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f51be04829e2a9c07e0f9602f02bc0a\\transformed\\material-1.10.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,4278,4389,4478,4579,4686,4793,4892,4999,5102,5190,5314,5416,5518,5634,5736,5850,5978,6094,6216,6352,6472,6606,6726,6838,7053,7170,7294,7424,7546,7684,7818,7934", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4273,4384,4473,4574,4681,4788,4887,4994,5097,5185,5309,5411,5513,5629,5731,5845,5973,6089,6211,6347,6467,6601,6721,6833,6959,7165,7289,7419,7541,7679,7813,7929,8049"}}, {"source": "D:\\Ankur\\EkvayuProjects\\Ekvayu\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,-1,-1,-1,71,58,77,65", "startColumns": "4,-1,-1,-1,4,4,4,4", "startOffsets": "111,-1,-1,-1,2940,2450,3152,2712", "endLines": "37,-1,-1,-1,75,63,81,69", "endColumns": "12,-1,-1,-1,12,12,12,12", "endOffsets": "1696,-1,-1,-1,3146,2706,3361,2934"}, "to": {"startLines": "12,48,52,56,61,66,72,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "626,2033,2231,2447,2658,2869,3130,3344", "endLines": "47,51,55,60,65,71,76,81", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "2028,2226,2442,2653,2864,3125,3339,3566"}}, {"source": "D:\\Ankur\\EkvayuProjects\\Ekvayu\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,152,204,264,316,386,440,494,558", "endColumns": "45,50,51,59,51,69,53,53,63,67", "endOffsets": "96,147,199,259,311,381,435,489,553,621"}}]}, {"outputFile": "com.tech.ekvayu.app-mergeDebugResources-37:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3a789a3e91da4aac57216b80449d8079\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "82,83,84,85,86,87,88,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3571,3641,3725,3809,3905,4007,4109,6964", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "3636,3720,3804,3900,4002,4104,4198,7048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1f51be04829e2a9c07e0f9602f02bc0a\\transformed\\material-1.10.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4203,4278,4389,4478,4579,4686,4793,4892,4999,5102,5190,5314,5416,5518,5634,5736,5850,5978,6094,6216,6352,6472,6606,6726,6838,7053,7170,7294,7424,7546,7684,7818,7934", "endColumns": "74,110,88,100,106,106,98,106,102,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "4273,4384,4473,4574,4681,4788,4887,4994,5097,5185,5309,5411,5513,5629,5731,5845,5973,6089,6211,6347,6467,6601,6721,6833,6959,7165,7289,7419,7541,7679,7813,7929,8049"}}, {"source": "D:\\Ankur\\EkvayuProjects\\Ekvayu\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,-1,-1,-1,-1,58,-1,-1", "startColumns": "4,-1,-1,-1,-1,4,-1,-1", "startOffsets": "111,-1,-1,-1,-1,2450,-1,-1", "endLines": "37,-1,-1,-1,-1,63,-1,-1", "endColumns": "12,-1,-1,-1,-1,12,-1,-1", "endOffsets": "1696,-1,-1,-1,-1,2706,-1,-1"}, "to": {"startLines": "12,48,52,56,61,66,72,77", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "626,2033,2231,2447,2658,2869,3130,3344", "endLines": "47,51,55,60,65,71,76,81", "endColumns": "12,12,12,12,12,12,12,12", "endOffsets": "2028,2226,2442,2653,2864,3125,3339,3566"}}, {"source": "D:\\Ankur\\EkvayuProjects\\Ekvayu\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,101,152,204,264,316,386,440,494,558", "endColumns": "45,50,51,59,51,69,53,53,63,67", "endOffsets": "96,147,199,259,311,381,435,489,553,621"}}]}]}