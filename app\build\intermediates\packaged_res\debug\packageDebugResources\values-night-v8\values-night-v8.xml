<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="app_color">#FF4040B0</color>
    <color name="app_color_dark">#FF2020A0</color>
    <color name="app_color_light">#FF6060D0</color>
    <color name="background">@color/background_dark</color>
    <color name="border">@color/border_dark</color>
    <color name="card_background">@color/card_background_dark</color>
    <color name="divider">@color/divider_dark</color>
    <color name="surface">@color/surface_dark</color>
    <color name="text_primary">@color/text_primary_dark</color>
    <color name="text_secondary">@color/text_secondary_dark</color>
    <style name="Base.Theme.Ekvayu" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/app_color</item>
        <item name="colorPrimaryVariant">@color/app_color_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        
        <item name="colorSecondary">@color/app_color_light</item>
        <item name="colorSecondaryVariant">@color/app_color</item>
        <item name="colorOnSecondary">@color/white</item>

        
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        
        <item name="android:navigationBarColor">@color/background</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowBackground">@color/background</item>

        
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>

        
        <item name="colorError">@color/red</item>
        <item name="colorOnError">@color/white</item>
    </style>
    <style name="ButtonPrimary" parent="Widget.Material3.Button">
        <item name="backgroundTint">@color/app_color</item>
        <item name="android:textColor">@color/white</item>
    </style>
    <style name="ButtonSecondary" parent="Widget.Material3.Button.OutlinedButton">
        <item name="strokeColor">@color/app_color</item>
        <item name="android:textColor">@color/app_color</item>
    </style>
    <style name="CardStyle">
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">8dp</item>
        <item name="cardCornerRadius">8dp</item>
    </style>
    <style name="HeaderButtonStyle">
        <item name="cardBackgroundColor">@color/app_color</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">4dp</item>
    </style>
    <style name="HeaderStyle">
        <item name="android:background">@color/surface</item>
        <item name="android:elevation">8dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="HeaderThemeButtonStyle">
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">4dp</item>
    </style>
    <style name="HeaderTitleStyle">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="fontFamily">@font/roboto_semi_bold</item>
    </style>
</resources>